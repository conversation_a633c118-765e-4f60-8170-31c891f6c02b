# **柴管家开发规范**

**版本:** 1.0 | **更新日期:** 2025-08-02 | **适用范围:** 全体开发人员

---

## **1. 开发思想与原则**

### **1.1 BDD核心理念**
- **三步走流程**: 编写Gherkin剧本 → 编写测试 → 编写代码
- **业务驱动**: 所有功能必须先有明确的业务场景定义
- **测试先行**: 代码实现前必须有失败的测试用例

### **1.2 代码质量标准**
- **测试覆盖率**: 核心业务逻辑 ≥ 85%
- **代码规范**: 严格遵循PEP 8，使用black格式化
- **文档要求**: 所有公共函数必须有docstring

### **1.3 团队协作原则**
- **GitHub为唯一信息源**: 所有任务状态和需求规格在GitHub管理
- **代码审查必须**: 任何代码合并前必须经过审查
- **持续集成**: 每次提交触发自动化测试和质量检查

---

## **2. 分阶段开发规范**

### **阶段1: 编写Gherkin剧本 (30分钟)**

**开发思想**: 用自然语言精确定义功能的预期行为，作为开发指令

**具体步骤**:
1. 在对应史诗目录下创建`.feature`文件
2. 使用Given-When-Then结构编写场景
3. 确保场景覆盖主要业务流程和边界情况
4. 产品负责人确认验收标准

**交付要求**:
- [ ] Feature描述清晰，包含用户角色、需求和价值
- [ ] Scenario覆盖主要业务流程和边界情况
- [ ] Given-When-Then结构完整且逻辑清晰

**示例**:
```gherkin
Feature: AI置信度处理与人工接管
  作为一个IP运营者
  我希望AI能根据置信度智能决策
  以便在保证效率的同时确保服务质量

  Scenario: 高置信度自动回复
    Given 系统已配置置信度阈值为0.8
    And 存在一个处于"AI托管模式"的会话
    When AI分析用户消息"课程多少钱"并生成置信度为0.9的回复
    Then 系统应该自动发送AI生成的回复
    And 会话状态保持为"AI托管中"
```

### **阶段2: 编写自动化测试 (60分钟)**

**开发思想**: 基于剧本生成当前会失败的自动化测试，确保测试驱动开发

**具体步骤**:
1. 创建单元测试文件 (`tests/unit/`)
2. 创建集成测试文件 (`tests/integration/`)
3. 编写对应Gherkin场景的测试用例
4. 运行测试确认失败（红灯状态）

**交付要求**:
- [ ] 单元测试覆盖核心业务逻辑
- [ ] 集成测试验证API端点完整流程
- [ ] 测试用例对应Gherkin场景
- [ ] 测试数据准备和清理完整

### **阶段3: 编写产品代码 (120分钟)**

**开发思想**: 编写刚好能让测试通过的最精简代码，避免过度设计

**具体步骤**:
1. 实现服务层业务逻辑
2. 创建API端点
3. 运行测试直到全部通过（绿灯状态）
4. 重构优化代码结构

**交付要求**:
- [ ] 代码符合PEP 8规范
- [ ] 函数和类有完整的文档字符串
- [ ] 错误处理和异常捕获完整
- [ ] 所有测试通过

---

## **3. 快速检查清单**

### **3.1 代码提交前检查**
```bash
# 运行检查命令
black app/ && isort app/ && flake8 app/ && mypy app/ && pytest tests/
```

- [ ] 代码格式化完成 (`black app/`)
- [ ] 导入排序完成 (`isort app/`)
- [ ] 静态检查通过 (`flake8 app/`)
- [ ] 类型检查通过 (`mypy app/`)
- [ ] 所有测试通过 (`pytest tests/`)
- [ ] 测试覆盖率 ≥ 85%

### **3.2 PR创建检查**
- [ ] 分支命名规范: `feature/epic-name/story-name`
- [ ] 提交信息格式: `feat(scope): description`
- [ ] CI流水线全部通过
- [ ] 代码审查请求已发送
- [ ] 相关Issue已关联

### **3.3 功能完成验收**
- [ ] 所有Gherkin场景的验收标准通过
- [ ] 代码已成功合并到develop分支
- [ ] API文档自动生成并更新
- [ ] 部署到测试环境成功
- [ ] 产品负责人验收通过

---

**📋 记住**: 严格遵循BDD三步走，确保每个功能都有明确的业务价值和完整的测试覆盖！
