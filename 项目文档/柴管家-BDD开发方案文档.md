# **柴管家 - BDD开发方案文档**

**版本:** 1.0  
**创建日期:** 2025-08-02  
**文档状态:** 已确认  

## **1. 项目概述**

### **1.1 产品愿景**

柴管家是一个多平台聚合智能客服系统，为知识类、教培类个人IP运营者提供一站式私域运营解决方案。通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

### **1.2 核心价值主张**

- **统一消息管理**: 将微信、抖音、小红书等多平台消息聚合到统一工作台
- **AI智能助理**: 提供AI副驾式回复建议和智能托管功能
- **人机协作**: 实现高置信度自动回复，低置信度人工接管的智能分流
- **知识库管理**: 构建可复用的FAQ知识库，提升回复效率

### **1.3 目标用户**

- **知识IP主理人**: 如职场成长博主，需要从重复咨询中解放精力
- **教培机构运营**: 如考研机构运营，需要从海量用户中筛选高意向线索

## **2. 开发方法论与项目结构**

### **2.1 BDD开发理念**

本项目采用**行为驱动开发（BDD）**作为核心开发策略，确保所有功能开发与业务需求精确对齐。

#### **BDD三步走流程**

```mermaid
graph TD
    A[1. 编写行为剧本<br/>Gherkin Feature Files] --> B[2. 编写自动化测试<br/>Test Implementation]
    B --> C[3. 编写产品代码<br/>Code Implementation]
    C --> D{测试通过?}
    D -- 是 --> E[功能完成]
    D -- 否 --> C
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style E fill:#c8e6c9
```

### **2.2 项目目录结构设计**

```
chaiguanjia_ag_8.3/
├── docs/                           # 项目文档
│   ├── api/                        # API文档
│   ├── architecture/               # 架构设计文档
│   └── deployment/                 # 部署文档
├── features/                       # BDD规范文件管理目录
│   ├── core_channel_management/    # 史诗1: 核心渠道管理
│   ├── unified_messaging/          # 史诗2: 统一消息工作台
│   ├── ai_copilot/                # 史诗3: AI副驾与知识库
│   └── ai_hosting/                # 史诗4: AI托管与人工接管
├── tests/                          # 测试文件管理目录
│   ├── unit/                       # 单元测试
│   ├── integration/                # 集成测试
│   ├── e2e/                        # 端到端测试
│   └── fixtures/                   # 测试数据
├── src/                            # 源代码
│   ├── backend/                    # 后端服务
│   │   ├── app/                    # FastAPI应用
│   │   │   ├── api/                # API路由
│   │   │   ├── core/               # 核心业务逻辑
│   │   │   ├── models/             # 数据模型
│   │   │   ├── services/           # 业务服务
│   │   │   └── utils/              # 工具函数
│   │   ├── connectors/             # 第三方平台连接器
│   │   │   ├── wechat/             # 微信连接器
│   │   │   ├── douyin/             # 抖音连接器
│   │   │   └── base/               # 连接器基类
│   │   └── database/               # 数据库相关
│   │       ├── migrations/         # 数据库迁移
│   │       └── seeds/              # 初始数据
│   ├── frontend/                   # 前端应用
│   │   ├── src/                    # React源码
│   │   │   ├── components/         # 可复用组件
│   │   │   ├── pages/              # 页面组件
│   │   │   ├── hooks/              # 自定义Hooks
│   │   │   ├── services/           # API服务
│   │   │   └── utils/              # 工具函数
│   │   └── public/                 # 静态资源
│   └── verification/               # 验证界面
│       ├── channel_management/     # 渠道管理验证页面
│       ├── messaging/              # 消息处理验证页面
│       ├── ai_copilot/            # AI副驾验证页面
│       └── ai_hosting/            # AI托管验证页面
├── infrastructure/                 # 基础设施配置
│   ├── docker/                     # Docker配置
│   ├── k8s/                        # Kubernetes配置
│   └── terraform/                  # 基础设施即代码
├── scripts/                        # 脚本文件
│   ├── setup/                      # 环境搭建脚本
│   ├── deployment/                 # 部署脚本
│   └── testing/                    # 测试脚本
└── .github/                        # GitHub配置
    ├── workflows/                  # GitHub Actions
    ├── ISSUE_TEMPLATE/             # Issue模板
    └── PULL_REQUEST_TEMPLATE.md    # PR模板
```

## **3. 技术架构设计**

### **3.1 架构风格**

采用**模块化单体 + 事件驱动连接器**架构：

```mermaid
graph TD
    subgraph "客户端层"
        A["Web前端<br/>(React SPA)"]
        B["验证界面<br/>(HTML Pages)"]
    end

    subgraph "服务端层"
        C["核心后端服务<br/>(Python/FastAPI)"]
        D["消息队列<br/>(RabbitMQ)"]
        E["主数据库<br/>(PostgreSQL)"]
        F["向量数据库<br/>(ChromaDB)"]
    end

    subgraph "连接器层"
        G1["微信连接器"]
        G2["抖音连接器"]
        G3["小红书连接器"]
    end

    subgraph "第三方平台"
        H1["微信"]
        H2["抖音"]
        H3["小红书"]
    end

    A -- "HTTPS/REST API<br/>WebSocket" --> C
    B -- "HTTP" --> C
    C -- "读写" --> E
    C -- "向量检索" --> F
    C -- "生产/消费" --> D

    G1 <--> H1
    G2 <--> H2
    G3 <--> H3

    G1 -- "消息队列" --> D
    G2 -- "消息队列" --> D
    G3 -- "消息队列" --> D
```

### **3.2 技术栈选型**

| 领域 | 技术/框架 | 选型理由 |
|------|-----------|----------|
| **后端** | Python + FastAPI | 团队熟悉，高性能异步IO，文档友好 |
| **前端** | React + Vite | 团队熟悉，开发体验佳，构建性能优秀 |
| **数据库** | PostgreSQL | 功能强大，支持JSON，社区活跃 |
| **向量数据库** | ChromaDB | Python原生，轻量，易于部署 |
| **消息队列** | RabbitMQ | 成熟可靠，支持多种消息模式 |
| **部署** | Docker + Docker Compose | 环境一致性，简化部署 |
| **测试** | pytest + Playwright | 全栈测试覆盖 |

## **4. 分阶段开发计划**

### **4.1 史诗拆解与优先级**

基于端到端故事切片策略，将MVP分为4个核心史诗：

#### **史诗1: 核心渠道管理 (Epic: Core Channel Management)**
- **目标**: 构建系统基础连接能力
- **用户故事**:
  - 作为运营者，我希望能接入微信账号，以便统一管理消息
  - 作为运营者，我希望能为接入的账号设置别名，以便区分不同用途
  - 作为运营者，我希望能监控账号连接状态，以便及时处理异常

#### **史诗2: 统一消息工作台 (Epic: Unified Messaging Workspace)**
- **目标**: 提供核心操作界面
- **用户故事**:
  - 作为运营者，我希望能在统一界面查看所有平台消息，以便提升效率
  - 作为运营者，我希望能直接回复任何平台的消息，以便无缝沟通
  - 作为运营者，我希望能查看完整对话历史，以便了解上下文

#### **史诗3: AI副驾与知识库 (Epic: AI Copilot & Knowledge Base)**
- **目标**: 为人工回复提效赋能
- **用户故事**:
  - 作为运营者，我希望能管理FAQ知识库，以便标准化回复
  - 作为运营者，我希望AI能分析用户意图，以便快速理解需求
  - 作为运营者，我希望AI能提供回复建议，以便提升回复质量

#### **史诗4: AI托管与人工接管 (Epic: AI Hosting & Manual Takeover)**
- **目标**: 实现运营自动化
- **用户故事**:
  - 作为运营者，我希望能开启AI托管模式，以便自动处理常见问题
  - 作为运营者，我希望AI在不确定时能自动转交，以便保证服务质量
  - 作为运营者，我希望能随时接管对话，以便处理复杂情况

### **4.2 开发时间线**

```mermaid
gantt
    title 柴管家开发路线图
    dateFormat  YYYY-MM-DD
    axisFormat %m-%d

    section 环境搭建 (Sprint 0)
    项目初始化与CI/CD :done, s0, 2025-08-04, 1w
    
    section MVP核心闭环 (Milestone 1)
    史诗1: 核心渠道管理 :active, s1, 2025-08-11, 2w
    史诗2: 统一消息工作台 :s2, after s1, 2w
    
    section AI能力增强 (Milestone 2)
    史诗3: AI副驾与知识库 :s3, after s2, 2w
    史诗4: AI托管与人工接管 :s4, after s3, 2w
```

## **5. BDD规范示例**

### **5.1 Feature文件结构**

每个史诗对应一个feature目录，包含多个.feature文件：

```
features/
├── core_channel_management/
│   ├── channel_connection.feature
│   ├── channel_management.feature
│   └── channel_monitoring.feature
```

### **5.2 Gherkin规范示例**

**文件**: `features/ai_hosting/ai_confidence_handling.feature`

```gherkin
Feature: AI置信度处理与人工接管
  作为一个IP运营者
  我希望AI能根据置信度智能决策
  以便在保证效率的同时确保服务质量

  Background:
    Given 系统已配置置信度阈值为0.8
    And 存在一个处于"AI托管模式"的会话

  Scenario: 高置信度自动回复
    Given AI分析用户消息"课程多少钱"
    When AI生成回复的置信度为0.9
    Then 系统应该自动发送AI生成的回复
    And 会话状态保持为"AI托管中"

  Scenario: 低置信度自动转交
    Given AI分析用户消息"我想退款但是找不到订单"
    When AI生成回复的置信度为0.6
    Then 系统绝不能自动发送该回复
    And 会话状态应该切换为"待人工接管"
    And 系统应该在界面上高亮该会话
    And 系统应该发送通知给运营者

  Scenario: 运营者手动接管
    Given 存在一个"待人工接管"状态的会话
    When 运营者点击"接管"按钮
    Then 会话状态应该切换为"人工模式"
    And 运营者应该能够正常发送消息
```

## **6. 测试策略与质量保证**

### **6.1 测试金字塔**

```mermaid
graph TD
    A["E2E测试<br/>(端到端测试)<br/>Playwright"] --> B["集成测试<br/>(API + 数据库)<br/>pytest"]
    B --> C["单元测试<br/>(业务逻辑)<br/>pytest + unittest"]

    style A fill:#ffcdd2
    style B fill:#fff3e0
    style C fill:#e8f5e8
```

### **6.2 测试覆盖策略**

#### **单元测试 (Unit Tests)**
- **覆盖率要求**: 核心业务逻辑 ≥ 85%
- **测试框架**: pytest
- **测试范围**:
  - 业务服务层逻辑
  - 数据模型验证
  - 工具函数
  - AI置信度计算算法

#### **集成测试 (Integration Tests)**
- **测试框架**: pytest + testcontainers
- **测试范围**:
  - API端点完整流程
  - 数据库操作
  - 消息队列通信
  - 第三方服务集成

#### **端到端测试 (E2E Tests)**
- **测试框架**: Playwright
- **测试范围**:
  - 用户完整操作流程
  - 跨平台消息收发
  - AI托管与人工接管流程
  - 界面交互验证

### **6.3 验证界面设计**

每个史诗创建专用的HTML验证页面：

#### **渠道管理验证页面** (`src/verification/channel_management/index.html`)
```html
<!DOCTYPE html>
<html>
<head>
    <title>渠道管理验证</title>
    <style>
        .test-section { margin: 20px; padding: 15px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>渠道管理功能验证</h1>

    <div class="test-section">
        <h2>1. 渠道连接测试</h2>
        <button onclick="testChannelConnection()">测试微信连接</button>
        <div id="connection-result"></div>
    </div>

    <div class="test-section">
        <h2>2. 渠道状态监控</h2>
        <button onclick="testChannelStatus()">检查连接状态</button>
        <div id="status-result"></div>
    </div>

    <script>
        async function testChannelConnection() {
            try {
                const response = await fetch('/api/v1/channels/test-connection', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ platform: 'wechat' })
                });
                const result = await response.json();
                document.getElementById('connection-result').innerHTML =
                    `<div class="success">连接成功: ${JSON.stringify(result)}</div>`;
            } catch (error) {
                document.getElementById('connection-result').innerHTML =
                    `<div class="error">连接失败: ${error.message}</div>`;
            }
        }

        async function testChannelStatus() {
            try {
                const response = await fetch('/api/v1/channels/status');
                const result = await response.json();
                document.getElementById('status-result').innerHTML =
                    `<div class="success">状态检查: ${JSON.stringify(result, null, 2)}</div>`;
            } catch (error) {
                document.getElementById('status-result').innerHTML =
                    `<div class="error">状态检查失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
```

### **6.4 完成定义 (Definition of Done)**

所有用户故事必须满足以下条件才能标记为完成：

1. ✅ **代码质量**: 通过CI/CD流水线的所有自动化检查
2. ✅ **测试覆盖**: 单元测试覆盖率 ≥ 85%，所有验收标准通过自动化测试
3. ✅ **集成验证**: 集成测试通过，API文档自动生成并更新
4. ✅ **可视化验证**: 对应的验证界面可正常运行并展示功能
5. ✅ **代码合并**: 代码已成功合并到主干分支
6. ✅ **文档更新**: 相关技术文档和用户文档已更新

## **7. CI/CD配置方案**

### **7.1 GitHub Actions工作流**

#### **主工作流** (`.github/workflows/main.yml`)

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: chaiguanjia_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      rabbitmq:
        image: rabbitmq:3-management
        env:
          RABBITMQ_DEFAULT_USER: admin
          RABBITMQ_DEFAULT_PASS: admin

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        cd src/backend
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run linting
      run: |
        cd src/backend
        flake8 app/
        black --check app/
        isort --check-only app/

    - name: Run unit tests
      run: |
        cd src/backend
        pytest tests/unit/ -v --cov=app --cov-report=xml

    - name: Run integration tests
      run: |
        cd src/backend
        pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/chaiguanjia_test
        RABBITMQ_URL: amqp://admin:admin@localhost:5672/

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./src/backend/coverage.xml

  frontend-test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/frontend/package-lock.json

    - name: Install dependencies
      run: |
        cd src/frontend
        npm ci

    - name: Run linting
      run: |
        cd src/frontend
        npm run lint

    - name: Run unit tests
      run: |
        cd src/frontend
        npm run test:coverage

    - name: Build application
      run: |
        cd src/frontend
        npm run build

  e2e-test:
    runs-on: ubuntu-latest
    needs: [test, frontend-test]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Start services
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30  # Wait for services to be ready

    - name: Run E2E tests
      run: |
        cd tests/e2e
        npm ci
        npx playwright test

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: tests/e2e/playwright-report/

  deploy:
    runs-on: ubuntu-latest
    needs: [test, frontend-test, e2e-test]
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Build and push Docker images
      run: |
        docker build -t chaiguanjia/backend:latest src/backend/
        docker build -t chaiguanjia/frontend:latest src/frontend/
        # Push to registry (配置实际的镜像仓库)

    - name: Deploy to staging
      run: |
        # 部署到测试环境的脚本
        echo "Deploying to staging environment"
```

### **7.2 代码质量检查配置**

#### **Python代码质量** (`.github/workflows/code-quality.yml`)

```yaml
name: Code Quality

on: [push, pull_request]

jobs:
  python-quality:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        cd src/backend
        pip install flake8 black isort mypy

    - name: Run flake8
      run: |
        cd src/backend
        flake8 app/ --max-line-length=88 --extend-ignore=E203,W503

    - name: Run black
      run: |
        cd src/backend
        black --check app/

    - name: Run isort
      run: |
        cd src/backend
        isort --check-only app/

    - name: Run mypy
      run: |
        cd src/backend
        mypy app/
```

## **8. GitHub项目管理配置指南**

### **8.1 Repository设置**

#### **分支保护规则**
- **主分支** (`main`):
  - 要求PR审查
  - 要求状态检查通过
  - 要求分支为最新
  - 限制推送权限

- **开发分支** (`develop`):
  - 要求状态检查通过
  - 允许强制推送

#### **Issue模板配置**

**用户故事模板** (`.github/ISSUE_TEMPLATE/user_story.md`)

```markdown
---
name: 用户故事
about: 创建一个新的用户故事
title: '[USER STORY] '
labels: user-story
assignees: ''
---

## 用户故事描述

**作为** [用户角色]
**我希望** [功能描述]
**以便** [价值/目标]

## 验收标准

- [ ] 验收标准1
- [ ] 验收标准2
- [ ] 验收标准3

## BDD场景

```gherkin
Feature: [功能名称]

Scenario: [场景名称]
  Given [前置条件]
  When [操作步骤]
  Then [预期结果]
```

## 技术任务

- [ ] 后端API开发
- [ ] 前端界面开发
- [ ] 单元测试编写
- [ ] 集成测试编写
- [ ] 验证界面创建

## 完成定义检查清单

- [ ] 代码通过CI/CD检查
- [ ] 单元测试覆盖率 ≥ 85%
- [ ] 所有验收标准通过自动化测试
- [ ] 验证界面可正常运行
- [ ] 代码已合并到主干分支
- [ ] 相关文档已更新
```

### **8.2 GitHub Projects配置**

#### **看板结构**

```mermaid
graph LR
    A[📋 Backlog<br/>产品待办] --> B[🔄 Sprint Backlog<br/>冲刺待办]
    B --> C[🚧 In Progress<br/>开发中]
    C --> D[🧪 Testing<br/>测试中]
    D --> E[👀 Review<br/>代码审查]
    E --> F[✅ Done<br/>已完成]

    style A fill:#f9f9f9
    style B fill:#fff3cd
    style C fill:#d1ecf1
    style D fill:#d4edda
    style E fill:#ffeaa7
    style F fill:#c8e6c9
```

#### **标签系统**

| 标签 | 颜色 | 用途 |
|------|------|------|
| `epic` | `#8B5CF6` | 史诗级功能 |
| `user-story` | `#3B82F6` | 用户故事 |
| `bug` | `#EF4444` | 缺陷修复 |
| `enhancement` | `#10B981` | 功能增强 |
| `documentation` | `#6B7280` | 文档相关 |
| `testing` | `#F59E0B` | 测试相关 |
| `priority-high` | `#DC2626` | 高优先级 |
| `priority-medium` | `#F59E0B` | 中优先级 |
| `priority-low` | `#10B981` | 低优先级 |

### **8.3 自动化工作流集成**

#### **Issue自动化** (`.github/workflows/issue-automation.yml`)

```yaml
name: Issue Automation

on:
  issues:
    types: [opened, labeled]

jobs:
  auto-assign-project:
    runs-on: ubuntu-latest
    steps:
    - name: Add to project
      uses: actions/add-to-project@v0.4.0
      with:
        project-url: https://github.com/users/YOUR_USERNAME/projects/1
        github-token: ${{ secrets.GITHUB_TOKEN }}

  auto-assign-epic:
    runs-on: ubuntu-latest
    if: contains(github.event.issue.labels.*.name, 'epic')
    steps:
    - name: Create epic milestone
      uses: actions/github-script@v6
      with:
        script: |
          const { data: milestone } = await github.rest.issues.createMilestone({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: `Epic: ${context.payload.issue.title}`,
            description: context.payload.issue.body
          });

          await github.rest.issues.update({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: context.payload.issue.number,
            milestone: milestone.number
          });
```

## **9. 实施指导与最佳实践**

### **9.1 开发流程**

1. **需求分析**: 产品负责人创建Epic Issue，拆解为User Story
2. **Sprint规划**: 团队选择User Story进入Sprint Backlog
3. **BDD开发**:
   - 编写Gherkin场景
   - 实现自动化测试
   - 开发功能代码
4. **质量保证**: 运行完整测试套件，创建验证界面
5. **代码审查**: 提交PR，通过同行评审
6. **部署验证**: 部署到测试环境，验证功能完整性

### **9.2 团队协作规范**

#### **分支命名规范**
- `feature/epic-name/story-name`: 功能分支
- `bugfix/issue-description`: 缺陷修复分支
- `hotfix/critical-issue`: 紧急修复分支

#### **提交信息规范**
```
type(scope): description

[optional body]

[optional footer]
```

**类型**:
- `feat`: 新功能
- `fix`: 缺陷修复
- `docs`: 文档更新
- `test`: 测试相关
- `refactor`: 代码重构

**示例**:
```
feat(ai-copilot): implement intent recognition for user messages

- Add intent classification using OpenAI API
- Implement confidence scoring mechanism
- Add unit tests for intent recognition service

Closes #123
```

### **9.3 监控与度量**

#### **关键指标**

| 指标类别 | 具体指标 | 目标值 |
|----------|----------|--------|
| **代码质量** | 测试覆盖率 | ≥ 85% |
| **代码质量** | 代码重复率 | ≤ 5% |
| **开发效率** | 平均Story完成时间 | ≤ 3天 |
| **开发效率** | Bug修复时间 | ≤ 1天 |
| **系统性能** | API响应时间 | ≤ 200ms |
| **系统性能** | 消息处理延迟 | ≤ 5秒 |

#### **持续改进**

- **每日站会**: 同步进度，识别阻碍
- **Sprint回顾**: 总结经验，优化流程
- **代码审查**: 知识分享，质量提升
- **技术债务管理**: 定期重构，保持代码健康

---

**文档版本**: 1.0
**最后更新**: 2025-08-02
**维护者**: 开发团队

> 本文档将随着项目进展持续更新，确保开发方案与实际实施保持同步。
